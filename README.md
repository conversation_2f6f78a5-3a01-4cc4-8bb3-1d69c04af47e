# Sort the Court: AI Edition

A sophisticated medieval kingdom management game inspired by "Sort the Court" where you play as a king making decisions for AI-generated NPCs in a dynamic, story-driven world.

## 🎮 Advanced Features

- **AI-Generated NPCs**: Each character is created by AI with unique names, personalities, and contextual requests
- **Sophisticated Consequence System**: AI analyzes each decision's impact on kingdom stats AND story progression
- **Dynamic Main Storyline**: Your decisions shape the overarching narrative of your reign
- **Story-Driven Endings**: Multiple branching endings based on your reputation and decision patterns
- **Contextual NPCs**: Characters reference your reputation, recent decisions, and current story events
- **Kingdom Reputation System**: Your ruling style affects how NPCs approach you
- **Decision Memory**: NPCs and events remember your past choices
- **Story Milestones**: Major decisions trigger significant story developments
- **Character Relationships**: Build relationships with different NPC types through your choices
- **Contextual Random Events**: Events that reflect your kingdom's current state and story phase

## 🏰 The Story

You are the newly crowned ruler of the Kingdom of Valdris, having inherited the throne after your father's unexpected death. The kingdom faces uncertainty, and your decisions will not only determine its fate but shape the very story of your reign.

## How to Play

1. **Setup**: Make sure you have a local AI server running on `http://127.0.0.1:4315` (the game uses this for AI generation)

2. **Gameplay**:
   - Each day, 5 AI-generated NPCs will approach your throne
   - Each NPC presents contextual requests based on your reputation and recent decisions
   - Listen to their requests and respond with YES or NO
   - Watch how your decisions affect both kingdom stats AND the ongoing story
   - Navigate story-driven events and random occurrences
   - Build your reputation and relationships through consistent decision-making
   - Work toward one of multiple possible endings based on your ruling style

3. **Controls**:
   - Console: The game runs automatically and shows output in the Godot console
   - UI: Connect the demo_ui.gd script to create a visual interface
   - API: Call `GameManager.make_decision("YES"/"NO")` from your code
   - Keyboard: Press Y for YES, N for NO (if using the UI script)

## Game Components

### GameManager (`game manager/game manager.gd`)
The sophisticated game controller that handles:
- Story-driven day progression and contextual NPC generation
- AI communication for character creation and consequence analysis
- Complex decision processing with story impact assessment
- Dynamic reputation and relationship management
- Story milestone tracking and branching narrative paths
- Multiple ending determination based on player choices

### Key Functions:
- `start_new_day()`: Begins a new day with story-contextual NPCs
- `generate_random_npc()`: Creates AI-powered characters aware of story state
- `make_decision(decision: String)`: Process player choices with AI consequence analysis
- `analyze_decision_consequences()`: Uses AI to determine realistic impacts
- `update_story_progression()`: Advances the main narrative based on decisions
- `get_game_state()`: Get current game and story information

## 🧠 Sophisticated Consequence System

Unlike simple stat-based systems, this game uses AI to analyze each decision:

1. **AI Analysis**: Each decision is sent to the AI with full context (kingdom state, reputation, recent decisions, story phase)
2. **Dynamic Consequences**: The AI determines realistic stat changes based on the specific request, not just NPC type
3. **Story Impact**: Every decision affects the overarching narrative and future NPC interactions
4. **Reputation Building**: Your decision patterns build a reputation that influences how NPCs approach you
5. **Contextual Requests**: NPCs reference your past decisions and current reputation in their requests
6. **Long-term Effects**: Decisions have consequences that may not appear until days later

### Example Consequence Analysis:
```
NPC Request: "Your Majesty, the harvest has failed and my village starves. Will you open the royal granaries?"
AI Analysis considers:
- Current food levels and treasury state
- Your reputation (generous vs. stingy)
- Recent similar decisions
- Story phase and ongoing events
- Long-term implications for kingdom stability
```

### Player2 (`player2/player2.gd`)
AI service manager that:
- Connects to local AI server (port 4315)
- Handles chat completions for NPC generation
- Manages AI conversation history

### CharacterManager (`character_manager/character_manager.gd`)
Character storage system that:
- Creates and stores NPC data
- Maintains chat history for each character
- Provides character lookup functionality

## Kingdom Stats

- **Population**: Affects kingdom size and tax income
- **Happiness**: Affects loyalty and productivity
- **Treasury**: Gold for funding projects and military
- **Military**: Defense against threats
- **Food**: Keeps population healthy and growing

## NPC Types

The game generates 10 different types of NPCs:
- **Merchant**: Trade and economic requests
- **Farmer**: Agriculture and food-related issues
- **Soldier**: Military and defense matters
- **Noble**: Political and social requests
- **Peasant**: Common people's concerns
- **Wizard**: Magical and mystical requests
- **Priest**: Religious and moral guidance
- **Thief**: Criminal justice decisions
- **Diplomat**: Foreign relations
- **Inventor**: Innovation and technology

## Win/Lose Conditions

### Game Over:
- Population reaches 0
- Happiness reaches 0 (revolution)
- Treasury drops below -500 (bankruptcy)
- No military with large population (conquest)
- No food with large population (famine)

### Victory:
- Survive 30+ days with Population ≥ 200 and Happiness ≥ 80

## Setup Instructions

1. **AI Server**: Ensure you have a compatible AI server running on port 4315
2. **Godot**: Open the project in Godot 4.x
3. **Run**: The game starts automatically when you run the main scene
4. **UI (Optional)**: Attach the demo_ui.gd script to a Control node for visual interface

## Customization

### Adding New NPC Types:
Edit the `npc_types` array in `generate_random_npc()` and add corresponding consequence logic in `apply_decision_consequences()`.

### Modifying Stats:
Adjust the `kingdom_stats` dictionary and consequence values to balance gameplay.

### Custom Events:
Add new random events in the `apply_random_daily_events()` function.

## Technical Requirements

- Godot 4.x
- Local AI server compatible with OpenAI API format
- HTTP request capabilities

## Signals for UI Integration

The GameManager emits these signals for UI updates:
- `stats_updated(stats)`: When kingdom stats change
- `new_question(npc_name, question, context)`: When NPC presents request
- `day_ended(day_number)`: When day concludes
- `game_ended(reason)`: When game ends (win/lose)

Connect these signals to your UI elements for a complete visual experience!
