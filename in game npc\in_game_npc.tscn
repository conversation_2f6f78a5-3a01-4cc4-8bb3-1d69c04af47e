[gd_scene load_steps=250 format=3 uid="uid://lugmogasqncr"]

[ext_resource type="Texture2D" uid="uid://dh0pnasvk4nc0" path="res://assets/Tiny Swords (Free Pack)/Units/Black Units/Monk/Idle.png" id="1_8t7x4"]
[ext_resource type="Texture2D" uid="uid://dxu04q2igkclf" path="res://assets/Tiny Swords (Free Pack)/Units/Black Units/Monk/Run.png" id="1_d3bcf"]
[ext_resource type="Script" uid="uid://cshi6mhlcfpmc" path="res://in game npc/in_game_npc.gd" id="1_jhfil"]
[ext_resource type="Texture2D" uid="uid://dq1kqsle4glmr" path="res://assets/Tiny Swords (Free Pack)/Units/Black Units/Warrior/Warrior_Idle.png" id="3_dlum0"]
[ext_resource type="Texture2D" uid="uid://dccdq8e34pydd" path="res://assets/Tiny Swords (Free Pack)/Units/Black Units/Warrior/Warrior_Run.png" id="4_qqvq4"]
[ext_resource type="Texture2D" uid="uid://c56k6opg8fw2b" path="res://assets/Tiny Swords (Free Pack)/Units/Black Units/Lancer/Lancer_Idle.png" id="5_8jmee"]
[ext_resource type="Texture2D" uid="uid://bhjxn086nk23" path="res://assets/Tiny Swords (Free Pack)/Units/Black Units/Lancer/Lancer_Run.png" id="6_ro6a2"]
[ext_resource type="Texture2D" uid="uid://bust40jdbn2ru" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Archer/Archer_Idle.png" id="7_8jk5p"]
[ext_resource type="Texture2D" uid="uid://b3t5m3jie222l" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Archer/Archer_Run.png" id="8_a7lvo"]
[ext_resource type="Texture2D" uid="uid://bqkplmcqae7pw" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Lancer/Lancer_Idle.png" id="9_f6did"]
[ext_resource type="Texture2D" uid="uid://cx0g3uybfots8" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Lancer/Lancer_Run.png" id="10_dchfk"]
[ext_resource type="Texture2D" uid="uid://bde3b1mgh7r1u" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Monk/Idle.png" id="11_g5lde"]
[ext_resource type="Texture2D" uid="uid://bbnjk85r274hj" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Monk/Run.png" id="12_b6xyr"]
[ext_resource type="Texture2D" uid="uid://dgfxk4vpdxsnh" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Warrior/Warrior_Idle.png" id="13_b6xyr"]
[ext_resource type="Texture2D" uid="uid://c81c5cxdt83up" path="res://assets/Tiny Swords (Free Pack)/Units/Blue Units/Warrior/Warrior_Run.png" id="14_o74hh"]
[ext_resource type="Texture2D" uid="uid://d1sc016s7awu5" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Archer/Archer_Idle.png" id="15_ba00n"]
[ext_resource type="Texture2D" uid="uid://car3nwthrupgf" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Archer/Archer_Run.png" id="16_aaqap"]
[ext_resource type="Texture2D" uid="uid://cp54f7cko7tpg" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Lancer/Lancer_Idle.png" id="17_r5ev0"]
[ext_resource type="Texture2D" uid="uid://bk5n2r3arbfm1" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Lancer/Lancer_Run.png" id="18_x3nkb"]
[ext_resource type="Texture2D" uid="uid://dulefl04j30tb" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Monk/Idle.png" id="19_gsosi"]
[ext_resource type="Texture2D" uid="uid://chjijur3iiaoh" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Monk/Run.png" id="20_r2bsu"]
[ext_resource type="Texture2D" uid="uid://bnynjxjce3pq3" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Warrior/Warrior_Idle.png" id="21_6jny2"]
[ext_resource type="Texture2D" uid="uid://j5nwkj3aic84" path="res://assets/Tiny Swords (Free Pack)/Units/Red Units/Warrior/Warrior_Run.png" id="22_423nh"]
[ext_resource type="Texture2D" uid="uid://j7v4cd341k83" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Archer/Archer_Idle.png" id="23_41ic4"]
[ext_resource type="Texture2D" uid="uid://ykmk1aaa0pvo" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Archer/Archer_Run.png" id="24_um8f3"]
[ext_resource type="Texture2D" uid="uid://ijsmdn26cb0y" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Lancer/Lancer_Idle.png" id="25_um8f3"]
[ext_resource type="Texture2D" uid="uid://n15trrlkcu53" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Lancer/Lancer_Run.png" id="26_4fdy3"]
[ext_resource type="Texture2D" uid="uid://byqihw4brc250" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Monk/Idle.png" id="27_q0mgg"]
[ext_resource type="Texture2D" uid="uid://dc1avbxtm84qw" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Monk/Run.png" id="28_k00y7"]
[ext_resource type="Texture2D" uid="uid://ssfbnhenwjtn" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Warrior/Warrior_Idle.png" id="29_k00y7"]
[ext_resource type="Texture2D" uid="uid://d0qg4c12wh63t" path="res://assets/Tiny Swords (Free Pack)/Units/Yellow Units/Warrior/Warrior_Run.png" id="30_6nptx"]
[ext_resource type="Texture2D" uid="uid://cchvsblesi4ax" path="res://assets/speech bubble.png" id="32_38j11"]
[ext_resource type="FontFile" uid="uid://k8gj5kbqq6bf" path="res://Jersey_10/Jersey10-Regular.ttf" id="33_ha2e2"]

[sub_resource type="AtlasTexture" id="AtlasTexture_dlum0"]
atlas = ExtResource("1_8t7x4")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_qqvq4"]
atlas = ExtResource("1_8t7x4")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_8jmee"]
atlas = ExtResource("1_8t7x4")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_a7lvo"]
atlas = ExtResource("1_8t7x4")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_f6did"]
atlas = ExtResource("1_8t7x4")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_dchfk"]
atlas = ExtResource("1_8t7x4")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_d3bcf"]
atlas = ExtResource("1_d3bcf")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ro6a2"]
atlas = ExtResource("1_d3bcf")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_hb4lw"]
atlas = ExtResource("1_d3bcf")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_8jk5p"]
atlas = ExtResource("1_d3bcf")
region = Rect2(576, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_ro6a2"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_dlum0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qqvq4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8jmee")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a7lvo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_f6did")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dchfk")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_d3bcf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ro6a2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hb4lw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8jk5p")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_g5lde"]
atlas = ExtResource("3_dlum0")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_b6xyr"]
atlas = ExtResource("3_dlum0")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_o74hh"]
atlas = ExtResource("3_dlum0")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_xkfyt"]
atlas = ExtResource("3_dlum0")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ba00n"]
atlas = ExtResource("3_dlum0")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_aaqap"]
atlas = ExtResource("3_dlum0")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_r5ev0"]
atlas = ExtResource("3_dlum0")
region = Rect2(1152, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_x3nkb"]
atlas = ExtResource("3_dlum0")
region = Rect2(1344, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_gsosi"]
atlas = ExtResource("4_qqvq4")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_r2bsu"]
atlas = ExtResource("4_qqvq4")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_6jny2"]
atlas = ExtResource("4_qqvq4")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_423nh"]
atlas = ExtResource("4_qqvq4")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_41ic4"]
atlas = ExtResource("4_qqvq4")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_um8f3"]
atlas = ExtResource("4_qqvq4")
region = Rect2(960, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_8t7x4"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_g5lde")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b6xyr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o74hh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xkfyt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ba00n")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_aaqap")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r5ev0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x3nkb")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_gsosi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r2bsu")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6jny2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_423nh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_41ic4")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_um8f3")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_4fdy3"]
atlas = ExtResource("5_8jmee")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_oew03"]
atlas = ExtResource("5_8jmee")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_q0mgg"]
atlas = ExtResource("5_8jmee")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_k00y7"]
atlas = ExtResource("5_8jmee")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_6nptx"]
atlas = ExtResource("5_8jmee")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_jhfil"]
atlas = ExtResource("5_8jmee")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_38j11"]
atlas = ExtResource("5_8jmee")
region = Rect2(1920, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_ha2e2"]
atlas = ExtResource("5_8jmee")
region = Rect2(2240, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_6yyy5"]
atlas = ExtResource("5_8jmee")
region = Rect2(2560, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_suhr7"]
atlas = ExtResource("5_8jmee")
region = Rect2(2880, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_r07f6"]
atlas = ExtResource("5_8jmee")
region = Rect2(3200, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_whsqb"]
atlas = ExtResource("5_8jmee")
region = Rect2(3520, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_de2ov"]
atlas = ExtResource("6_ro6a2")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_h6c8p"]
atlas = ExtResource("6_ro6a2")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_u7bvk"]
atlas = ExtResource("6_ro6a2")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_bjdlx"]
atlas = ExtResource("6_ro6a2")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_0ooxk"]
atlas = ExtResource("6_ro6a2")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_2611w"]
atlas = ExtResource("6_ro6a2")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="SpriteFrames" id="SpriteFrames_qqvq4"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_4fdy3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_oew03")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q0mgg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k00y7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6nptx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_jhfil")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_38j11")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ha2e2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6yyy5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_suhr7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_r07f6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_whsqb")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_de2ov")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h6c8p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_u7bvk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bjdlx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0ooxk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2611w")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_y2py5"]
atlas = ExtResource("7_8jk5p")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_of7u2"]
atlas = ExtResource("7_8jk5p")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_67f3e"]
atlas = ExtResource("7_8jk5p")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ukfef"]
atlas = ExtResource("7_8jk5p")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_hd8kv"]
atlas = ExtResource("7_8jk5p")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_0sick"]
atlas = ExtResource("7_8jk5p")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_jjjf6"]
atlas = ExtResource("8_a7lvo")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_kyjbr"]
atlas = ExtResource("8_a7lvo")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_7hlcg"]
atlas = ExtResource("8_a7lvo")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_hlvlv"]
atlas = ExtResource("8_a7lvo")
region = Rect2(576, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_b2ren"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_y2py5")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_of7u2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_67f3e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ukfef")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hd8kv")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0sick")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_jjjf6")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kyjbr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7hlcg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_hlvlv")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_b2ren"]
atlas = ExtResource("9_f6did")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_j7uc2"]
atlas = ExtResource("9_f6did")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_i8icq"]
atlas = ExtResource("9_f6did")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_iknht"]
atlas = ExtResource("9_f6did")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_1y83u"]
atlas = ExtResource("9_f6did")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_dm4jm"]
atlas = ExtResource("9_f6did")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_mdrxy"]
atlas = ExtResource("9_f6did")
region = Rect2(1920, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_mnwqr"]
atlas = ExtResource("9_f6did")
region = Rect2(2240, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_h2txb"]
atlas = ExtResource("9_f6did")
region = Rect2(2560, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_44knb"]
atlas = ExtResource("9_f6did")
region = Rect2(2880, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_ojouf"]
atlas = ExtResource("9_f6did")
region = Rect2(3200, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_nkcfw"]
atlas = ExtResource("9_f6did")
region = Rect2(3520, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_2qt2u"]
atlas = ExtResource("10_dchfk")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_weby7"]
atlas = ExtResource("10_dchfk")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_gkakw"]
atlas = ExtResource("10_dchfk")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_1nq83"]
atlas = ExtResource("10_dchfk")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_auetx"]
atlas = ExtResource("10_dchfk")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_iok87"]
atlas = ExtResource("10_dchfk")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="SpriteFrames" id="SpriteFrames_0hdg0"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_b2ren")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_j7uc2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_i8icq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iknht")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1y83u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dm4jm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mdrxy")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mnwqr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_h2txb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_44knb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ojouf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nkcfw")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_2qt2u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_weby7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gkakw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1nq83")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_auetx")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iok87")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_0hdg0"]
atlas = ExtResource("11_g5lde")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_yxrqi"]
atlas = ExtResource("11_g5lde")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_shb54"]
atlas = ExtResource("11_g5lde")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_qysuj"]
atlas = ExtResource("11_g5lde")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_kh63b"]
atlas = ExtResource("11_g5lde")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_it7xw"]
atlas = ExtResource("11_g5lde")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_5c0v2"]
atlas = ExtResource("12_b6xyr")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_8duk3"]
atlas = ExtResource("12_b6xyr")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_n6ft0"]
atlas = ExtResource("12_b6xyr")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_b87nk"]
atlas = ExtResource("12_b6xyr")
region = Rect2(576, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_r5bif"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_0hdg0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yxrqi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_shb54")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_qysuj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_kh63b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_it7xw")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_5c0v2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8duk3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n6ft0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_b87nk")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_r5bif"]
atlas = ExtResource("13_b6xyr")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_biwxa"]
atlas = ExtResource("13_b6xyr")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_8ebo2"]
atlas = ExtResource("13_b6xyr")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_mm55m"]
atlas = ExtResource("13_b6xyr")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_dwdjc"]
atlas = ExtResource("13_b6xyr")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_m2s0s"]
atlas = ExtResource("13_b6xyr")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_bm30p"]
atlas = ExtResource("13_b6xyr")
region = Rect2(1152, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_iu8ij"]
atlas = ExtResource("13_b6xyr")
region = Rect2(1344, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_wsxcl"]
atlas = ExtResource("14_o74hh")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_bylpt"]
atlas = ExtResource("14_o74hh")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_fimvb"]
atlas = ExtResource("14_o74hh")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_rwdxc"]
atlas = ExtResource("14_o74hh")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_fc4ch"]
atlas = ExtResource("14_o74hh")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_yhh5t"]
atlas = ExtResource("14_o74hh")
region = Rect2(960, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_wsxcl"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_r5bif")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_biwxa")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8ebo2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mm55m")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dwdjc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m2s0s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bm30p")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_iu8ij")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_wsxcl")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bylpt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fimvb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rwdxc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fc4ch")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_yhh5t")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_sum2c"]
atlas = ExtResource("15_ba00n")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_adg6b"]
atlas = ExtResource("15_ba00n")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_a84r7"]
atlas = ExtResource("15_ba00n")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_1vr5v"]
atlas = ExtResource("15_ba00n")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ajn33"]
atlas = ExtResource("15_ba00n")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_4xigb"]
atlas = ExtResource("15_ba00n")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_83jew"]
atlas = ExtResource("16_aaqap")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_urjvo"]
atlas = ExtResource("16_aaqap")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_nlecr"]
atlas = ExtResource("16_aaqap")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_spv4m"]
atlas = ExtResource("16_aaqap")
region = Rect2(576, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_1c11k"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_sum2c")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_adg6b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_a84r7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1vr5v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ajn33")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4xigb")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_83jew")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_urjvo")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nlecr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_spv4m")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_1c11k"]
atlas = ExtResource("17_r5ev0")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_667hc"]
atlas = ExtResource("17_r5ev0")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_if5y8"]
atlas = ExtResource("17_r5ev0")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_eooo0"]
atlas = ExtResource("17_r5ev0")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_xblvt"]
atlas = ExtResource("17_r5ev0")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_dsst7"]
atlas = ExtResource("17_r5ev0")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_y1624"]
atlas = ExtResource("17_r5ev0")
region = Rect2(1920, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_41xp1"]
atlas = ExtResource("17_r5ev0")
region = Rect2(2240, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_2lxqt"]
atlas = ExtResource("17_r5ev0")
region = Rect2(2560, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_bv2r2"]
atlas = ExtResource("17_r5ev0")
region = Rect2(2880, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_k1le3"]
atlas = ExtResource("17_r5ev0")
region = Rect2(3200, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_6wle3"]
atlas = ExtResource("17_r5ev0")
region = Rect2(3520, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_3bj68"]
atlas = ExtResource("18_x3nkb")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_5v4ia"]
atlas = ExtResource("18_x3nkb")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_ea6ll"]
atlas = ExtResource("18_x3nkb")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_668mi"]
atlas = ExtResource("18_x3nkb")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_4fb1k"]
atlas = ExtResource("18_x3nkb")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_o6s5p"]
atlas = ExtResource("18_x3nkb")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="SpriteFrames" id="SpriteFrames_k0b2g"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1c11k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_667hc")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_if5y8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_eooo0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_xblvt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dsst7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_y1624")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_41xp1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_2lxqt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bv2r2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_k1le3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6wle3")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_3bj68")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5v4ia")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ea6ll")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_668mi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4fb1k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o6s5p")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_k0b2g"]
atlas = ExtResource("19_gsosi")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_7dwu3"]
atlas = ExtResource("19_gsosi")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_86nke"]
atlas = ExtResource("19_gsosi")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_68f4v"]
atlas = ExtResource("19_gsosi")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_swr5k"]
atlas = ExtResource("19_gsosi")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_8hve0"]
atlas = ExtResource("19_gsosi")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_p5ye0"]
atlas = ExtResource("20_r2bsu")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ejtak"]
atlas = ExtResource("20_r2bsu")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_6ebnh"]
atlas = ExtResource("20_r2bsu")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_7hewi"]
atlas = ExtResource("20_r2bsu")
region = Rect2(576, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_n48sk"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_k0b2g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7dwu3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_86nke")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_68f4v")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_swr5k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8hve0")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_p5ye0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ejtak")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6ebnh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_7hewi")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_n48sk"]
atlas = ExtResource("21_6jny2")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_6dwer"]
atlas = ExtResource("21_6jny2")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_vrm8b"]
atlas = ExtResource("21_6jny2")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_5gdho"]
atlas = ExtResource("21_6jny2")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_dll3u"]
atlas = ExtResource("21_6jny2")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_17m52"]
atlas = ExtResource("21_6jny2")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_mbfyh"]
atlas = ExtResource("21_6jny2")
region = Rect2(1152, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_nigbc"]
atlas = ExtResource("21_6jny2")
region = Rect2(1344, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_fxmvg"]
atlas = ExtResource("22_423nh")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_723in"]
atlas = ExtResource("22_423nh")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_x72gm"]
atlas = ExtResource("22_423nh")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_m0t1n"]
atlas = ExtResource("22_423nh")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_1si3o"]
atlas = ExtResource("22_423nh")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ypyop"]
atlas = ExtResource("22_423nh")
region = Rect2(960, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_ffqcr"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_n48sk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6dwer")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_vrm8b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5gdho")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dll3u")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_17m52")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mbfyh")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nigbc")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_fxmvg")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_723in")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_x72gm")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m0t1n")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_1si3o")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ypyop")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_ffqcr"]
atlas = ExtResource("23_41ic4")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_88p3y"]
atlas = ExtResource("23_41ic4")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_w2pl0"]
atlas = ExtResource("23_41ic4")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_arn7g"]
atlas = ExtResource("23_41ic4")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_owpwf"]
atlas = ExtResource("23_41ic4")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_0fbo0"]
atlas = ExtResource("23_41ic4")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_1ims7"]
atlas = ExtResource("24_um8f3")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_rpbi2"]
atlas = ExtResource("24_um8f3")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_rwduf"]
atlas = ExtResource("24_um8f3")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_p3oh6"]
atlas = ExtResource("24_um8f3")
region = Rect2(576, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_jwrmd"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_ffqcr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_88p3y")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_w2pl0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_arn7g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_owpwf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0fbo0")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_1ims7")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rpbi2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_rwduf")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_p3oh6")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_jwrmd"]
atlas = ExtResource("25_um8f3")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_72s3x"]
atlas = ExtResource("25_um8f3")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_pvrbt"]
atlas = ExtResource("25_um8f3")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_pxces"]
atlas = ExtResource("25_um8f3")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_6ynd3"]
atlas = ExtResource("25_um8f3")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_0qgy1"]
atlas = ExtResource("25_um8f3")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_45gth"]
atlas = ExtResource("25_um8f3")
region = Rect2(1920, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_m26mj"]
atlas = ExtResource("25_um8f3")
region = Rect2(2240, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_4f8bi"]
atlas = ExtResource("25_um8f3")
region = Rect2(2560, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_fd6v3"]
atlas = ExtResource("25_um8f3")
region = Rect2(2880, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_6eq8j"]
atlas = ExtResource("25_um8f3")
region = Rect2(3200, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_3m6n1"]
atlas = ExtResource("25_um8f3")
region = Rect2(3520, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_v4gll"]
atlas = ExtResource("26_4fdy3")
region = Rect2(0, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_uwo7j"]
atlas = ExtResource("26_4fdy3")
region = Rect2(320, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_8vx7k"]
atlas = ExtResource("26_4fdy3")
region = Rect2(640, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_6u55e"]
atlas = ExtResource("26_4fdy3")
region = Rect2(960, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_dpvxs"]
atlas = ExtResource("26_4fdy3")
region = Rect2(1280, 0, 320, 320)

[sub_resource type="AtlasTexture" id="AtlasTexture_cxv2s"]
atlas = ExtResource("26_4fdy3")
region = Rect2(1600, 0, 320, 320)

[sub_resource type="SpriteFrames" id="SpriteFrames_v4gll"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_jwrmd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_72s3x")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pvrbt")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_pxces")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6ynd3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_0qgy1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_45gth")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m26mj")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4f8bi")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_fd6v3")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6eq8j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_3m6n1")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_v4gll")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_uwo7j")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8vx7k")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_6u55e")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dpvxs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_cxv2s")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_yagk8"]
atlas = ExtResource("27_q0mgg")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_531ar"]
atlas = ExtResource("27_q0mgg")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_gocp2"]
atlas = ExtResource("27_q0mgg")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_q8wlb"]
atlas = ExtResource("27_q0mgg")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_5lsky"]
atlas = ExtResource("27_q0mgg")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_bhhfl"]
atlas = ExtResource("27_q0mgg")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_fd1pk"]
atlas = ExtResource("28_k00y7")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_dwv6g"]
atlas = ExtResource("28_k00y7")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_4iljs"]
atlas = ExtResource("28_k00y7")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_mhvb8"]
atlas = ExtResource("28_k00y7")
region = Rect2(576, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_kwhrr"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_yagk8")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_531ar")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gocp2")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_q8wlb")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_5lsky")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_bhhfl")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_fd1pk")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_dwv6g")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_4iljs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_mhvb8")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="AtlasTexture" id="AtlasTexture_kwhrr"]
atlas = ExtResource("29_k00y7")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_8krcd"]
atlas = ExtResource("29_k00y7")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_l8jqs"]
atlas = ExtResource("29_k00y7")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ej1q0"]
atlas = ExtResource("29_k00y7")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ndl1b"]
atlas = ExtResource("29_k00y7")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_ygkvq"]
atlas = ExtResource("29_k00y7")
region = Rect2(960, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_25u6s"]
atlas = ExtResource("29_k00y7")
region = Rect2(1152, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_n1njc"]
atlas = ExtResource("29_k00y7")
region = Rect2(1344, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_7vmkw"]
atlas = ExtResource("30_6nptx")
region = Rect2(0, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_gacir"]
atlas = ExtResource("30_6nptx")
region = Rect2(192, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_o6dob"]
atlas = ExtResource("30_6nptx")
region = Rect2(384, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_m4ar1"]
atlas = ExtResource("30_6nptx")
region = Rect2(576, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_nlh4i"]
atlas = ExtResource("30_6nptx")
region = Rect2(768, 0, 192, 192)

[sub_resource type="AtlasTexture" id="AtlasTexture_wost3"]
atlas = ExtResource("30_6nptx")
region = Rect2(960, 0, 192, 192)

[sub_resource type="SpriteFrames" id="SpriteFrames_7vmkw"]
animations = [{
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_kwhrr")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_8krcd")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_l8jqs")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ej1q0")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ndl1b")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_ygkvq")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_25u6s")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_n1njc")
}],
"loop": true,
"name": &"default",
"speed": 5.0
}, {
"frames": [{
"duration": 1.0,
"texture": SubResource("AtlasTexture_7vmkw")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_gacir")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_o6dob")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_m4ar1")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_nlh4i")
}, {
"duration": 1.0,
"texture": SubResource("AtlasTexture_wost3")
}],
"loop": true,
"name": &"walk",
"speed": 5.0
}]

[sub_resource type="Animation" id="Animation_8jmee"]
length = 0.001
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Graphics:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0),
"transitions": PackedFloat32Array(1),
"update": 0,
"values": [Vector2(0, 0)]
}

[sub_resource type="Animation" id="Animation_qqvq4"]
resource_name = "walk in"
length = 4.0
tracks/0/type = "value"
tracks/0/imported = false
tracks/0/enabled = true
tracks/0/path = NodePath("Graphics:position")
tracks/0/interp = 1
tracks/0/loop_wrap = true
tracks/0/keys = {
"times": PackedFloat32Array(0, 4),
"transitions": PackedFloat32Array(1, 1),
"update": 0,
"values": [Vector2(217, 0), Vector2(0, 0)]
}

[sub_resource type="AnimationLibrary" id="AnimationLibrary_8jmee"]
_data = {
&"RESET": SubResource("Animation_8jmee"),
&"walk in": SubResource("Animation_qqvq4")
}

[node name="In Game NPC" type="Node2D"]
z_index = 2
script = ExtResource("1_jhfil")

[node name="Graphics" type="Node2D" parent="."]

[node name="Monk Black" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_ro6a2")
autoplay = "default"
flip_h = true

[node name="Warrior Black" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_8t7x4")
animation = &"walk"
autoplay = "default"
frame_progress = 0.216773
flip_h = true

[node name="Lancer Black" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_qqvq4")
animation = &"walk"
autoplay = "default"
flip_h = true

[node name="Archer Blue" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_b2ren")
animation = &"walk"
autoplay = "default"
flip_h = true

[node name="Lancer Blue" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_0hdg0")
autoplay = "default"
flip_h = true

[node name="Monk Blue" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_r5bif")
autoplay = "default"
flip_h = true

[node name="Warrior Blue" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_wsxcl")
animation = &"walk"
autoplay = "default"
flip_h = true

[node name="Archer Red" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_1c11k")
autoplay = "default"
flip_h = true

[node name="Lancer Red" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_k0b2g")
animation = &"walk"
autoplay = "default"
flip_h = true

[node name="Monk Red" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_n48sk")
autoplay = "default"
flip_h = true

[node name="Warrior Red" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_ffqcr")
animation = &"walk"
autoplay = "default"
flip_h = true

[node name="Archer Yellow" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_jwrmd")
autoplay = "default"
flip_h = true

[node name="Lancer Yellow" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_v4gll")
animation = &"walk"
autoplay = "default"
flip_h = true

[node name="Monk Yellow" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_kwhrr")
autoplay = "default"
flip_h = true

[node name="Warrior Yellow" type="AnimatedSprite2D" parent="Graphics"]
visible = false
position = Vector2(1006, 486)
sprite_frames = SubResource("SpriteFrames_7vmkw")
animation = &"walk"
autoplay = "default"
flip_h = true

[node name="AnimationPlayer" type="AnimationPlayer" parent="."]
libraries = {
&"": SubResource("AnimationLibrary_8jmee")
}
autoplay = "walk in"

[node name="SpeechBubble" type="Sprite2D" parent="."]
position = Vector2(759, 314)
scale = Vector2(0.323958, 0.323958)
texture = ExtResource("32_38j11")

[node name="Label" type="Label" parent="SpeechBubble"]
offset_left = -827.0
offset_top = -747.0
offset_right = 809.0
offset_bottom = -188.0
theme_override_colors/font_color = Color(0, 0, 0, 1)
theme_override_fonts/font = ExtResource("33_ha2e2")
theme_override_font_sizes/font_size = 50
text = "lorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsumlorem ipsum"
autowrap_mode = 2
