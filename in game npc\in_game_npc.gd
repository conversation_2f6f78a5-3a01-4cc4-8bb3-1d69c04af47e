extends Node2D

var current_active_sprite: AnimatedSprite2D = null

func show_sprite():
	current_active_sprite = $Graphics.get_children().pick_random()
	current_active_sprite.visible = true

func walk_in_and_display_text(text):
	current_active_sprite.animation = "walk"
	$AnimationPlayer.play("walk in")
	await $AnimationPlayer.animation_finished
	current_active_sprite.animation = "default"

func walk_out():
	current_active_sprite.flip_h = false
	current_active_sprite.animation = "walk"
	$AnimationPlayer.play_backwards("walk in")
	await $AnimationPlayer.animation_finished
	current_active_sprite.flip_h = true
	current_active_sprite.visible = false



