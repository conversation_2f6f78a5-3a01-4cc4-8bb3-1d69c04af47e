extends Node

@onready var http_request: HTTPRequest = HTTPRequest.new()

var server_ok := false
var model_name = "llama3.2:3b"  # Llama 3.2 3B model
var ollama_url = "http://127.0.0.1:11434"  # Default Ollama port

# Rate limiting to prevent overwhelming Ollama
var last_request_time = 0.0
var min_request_interval = 1.0  # 1 second between requests (Ollama is usually faster)
var active_requests = 0
var max_concurrent_requests = 2  # Ollama can handle more concurrent requests

func _ready():
	add_child(http_request)
	server_ok = await get_server_health()
	if server_ok:
		push_warning("Ollama server is ready with model: %s" % model_name)
		# # Test the API
		# var test_result = await create_chat_completion([{"role": "user", "content": "Hello! What is your name?"}], "You are a helpful assistant named <PERSON>.")
		# if test_result != "":
		# 	push_warning("Ollama test successful: " + test_result)
		# else:
		# 	push_error("Ollama test failed - check model availability")
	else:
		push_error("Ollama server is not available at %s" % ollama_url)

func get_server_health() -> bool:
	# Check if Ollama server is running
	var health_url = ollama_url + "/api/tags"
	http_request.request(health_url)
	var result = await http_request.request_completed
	var json = _process_result(result)

	# Check if our model is available
	if json.has("models"):
		for model in json["models"]:
			if model.has("name") and model_name in model["name"]:
				push_warning("Found model: %s" % model["name"])
				return true
		push_error("Model %s not found. Available models: %s" % [model_name, str(json.get("models", []))])
		return false

	return false

# Main API function - compatible with Player2 interface
func create_chat_completion(character_history: Array = [], additional_context: String = ""):
	# Wait for available request slot
	await _wait_for_request_slot()

	# Mark request as active
	active_requests += 1

	var result = ""
	var max_retries = 2

	for attempt in range(max_retries):
		# Wait for rate limiting
		await _wait_for_rate_limit()

		# Build messages for Ollama format
		var messages = []

		# Add system context first if provided
		if additional_context != "":
			messages.append({"role": "system", "content": additional_context})

		# Add character history
		for message in character_history:
			messages.append(message)

		# Create Ollama chat request body
		var request_body = {
			"model": model_name,
			"messages": messages,
			"stream": false,
			"options": {
				"temperature": 0.7,
				"top_p": 0.9,
				"max_tokens": 500
			}
		}

		# Convert to JSON
		var json_string = JSON.stringify(request_body)
		var headers = ["Content-Type: application/json"]
		var chat_url = ollama_url + "/api/chat"

		push_warning("Making Ollama request attempt %d/%d with %d messages" % [attempt + 1, max_retries, messages.size()])

		# Make the request
		http_request.request(chat_url, headers, HTTPClient.METHOD_POST, json_string)
		var http_result = await http_request.request_completed

		# Update last request time
		last_request_time = Time.get_unix_time_from_system()

		# Process the result
		var json = _process_result(http_result)
		print(json)

		# Extract response from Ollama format
		if json.has("message") and json["message"].has("content"):
			result = json["message"]["content"]
			break  # Success, exit retry loop

		# Check for errors
		var response_code = http_result[1]
		if response_code != 200:
			if attempt < max_retries - 1:
				var wait_time = (attempt + 1) * 2.0  # 2s, 4s backoff
				push_warning("Ollama request failed (code %d), waiting %.1f seconds before retry" % [response_code, wait_time])
				await get_tree().create_timer(wait_time).timeout
			else:
				push_error("Ollama request failed after %d attempts with code %d" % [max_retries, response_code])
		else:
			# Success but no content, don't retry
			break

	# Mark request as completed
	active_requests -= 1

	if result == "":
		push_error("Ollama request failed to get valid response")

	return result

# Wait for available request slot to prevent overwhelming Ollama
func _wait_for_request_slot():
	while active_requests >= max_concurrent_requests:
		push_warning("Waiting for Ollama request slot (currently %d/%d active)" % [active_requests, max_concurrent_requests])
		await get_tree().create_timer(0.5).timeout

# Rate limiting for Ollama requests
func _wait_for_rate_limit():
	var current_time = Time.get_unix_time_from_system()
	var time_since_last = current_time - last_request_time

	if time_since_last < min_request_interval:
		var wait_time = min_request_interval - time_since_last
		push_warning("Ollama rate limiting: waiting %.1f seconds" % wait_time)
		await get_tree().create_timer(wait_time).timeout

# Process HTTP response with Ollama-specific error handling
func _process_result(result: Array):
	var response_code = result[1]
	var body_bytes = result[3]
	var body_text = body_bytes.get_string_from_utf8()

	# Ollama-specific error handling
	if response_code != 200:
		if response_code == 404:
			push_error("ERROR 404: Model %s not found. Make sure it's installed with 'ollama pull %s'" % [model_name, model_name])
		elif response_code == 500:
			push_error("ERROR 500: Ollama internal server error. Check if model is loaded.")
		else:
			push_error("Ollama HTTP Error %d: %s" % [response_code, body_text])
		return {}

	var parsed = JSON.parse_string(body_text)
	if parsed == null:
		push_error("Ollama JSON Parse Error. Response body: %s" % body_text)
		return {}

	return parsed
