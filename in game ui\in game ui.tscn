[gd_scene load_steps=2 format=3 uid="uid://b7o3ta5eis42a"]

[ext_resource type="Script" uid="uid://cw7puc185q5g0" path="res://in game ui/in game ui.gd" id="1_itgh4"]

[node name="In Game UI" type="Control"]
z_index = 1
layout_mode = 3
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
grow_horizontal = 2
grow_vertical = 2
script = ExtResource("1_itgh4")

[node name="Button" type="Button" parent="."]
layout_mode = 0
offset_left = 924.0
offset_top = 562.0
offset_right = 959.0
offset_bottom = 593.0
text = "Yes"

[node name="Button2" type="Button" parent="."]
layout_mode = 0
offset_left = 1020.0
offset_top = 560.0
offset_right = 1055.0
offset_bottom = 591.0
text = "No"

[node name="ScrollContainer" type="ScrollContainer" parent="."]
layout_mode = 1
anchors_preset = 15
anchor_right = 1.0
anchor_bottom = 1.0
offset_left = 49.0
offset_right = -38.0
offset_bottom = -129.0
grow_horizontal = 2
grow_vertical = 2
horizontal_scroll_mode = 0

[node name="VBoxContainer" type="VBoxContainer" parent="ScrollContainer"]
layout_mode = 2
size_flags_vertical = 3

[node name="Label" type="Label" parent="ScrollContainer/VBoxContainer"]
custom_minimum_size = Vector2(500, 0)
layout_mode = 2
text = "Test"
autowrap_mode = 1

[connection signal="pressed" from="Button" to="." method="_on_button_pressed"]
[connection signal="pressed" from="Button2" to="." method="_on_button_2_pressed"]
