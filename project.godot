; Engine configuration file.
; It's best edited using the editor UI and not directly,
; since the parameters that go here are not all obvious.
;
; Format:
;   [section] ; section goes between []
;   param=value ; assign values to parameters

config_version=5

[application]

config/name="g-player2"
run/main_scene="uid://bklfst6d7wuxq"
config/features=PackedStringArray("4.4", "Forward Plus")
config/icon="res://icon.svg"

[autoload]

Ollama="*res://ollama/ollama.gd"
Player2="*res://player2/player2.gd"
CharacterManager="*res://character_manager/character_manager.gd"
MainStoryline="*res://main storyline/main storyline.gd"
InGameNpc="*res://in game npc/in_game_npc.tscn"
InGameUi="*res://in game ui/in game ui.tscn"
GameManager="*res://game manager/game manager.gd"

[display]

window/stretch/mode="canvas_items"
window/stretch/aspect="expand"
