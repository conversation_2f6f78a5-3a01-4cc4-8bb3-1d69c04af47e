{"merchant": [{"name": "<PERSON><PERSON> the Trader", "request": "Your Majesty, I seek permission to establish a new trade route through the northern pass. Will you grant me exclusive trading rights?", "yes_consequences": {"stat_changes": {"treasury": 50, "happiness": 10, "population": 5}, "story_impact": "New trade brings wealth but may anger other merchants"}, "no_consequences": {"stat_changes": {"treasury": -10, "happiness": -5}, "story_impact": "The merchant leaves disappointed, taking potential wealth elsewhere"}}, {"name": "<PERSON>", "request": "Your Majesty, foreign traders are undercutting our local merchants. Will you impose tariffs to protect our businesses?", "yes_consequences": {"stat_changes": {"treasury": 30, "happiness": 15, "population": -5}, "story_impact": "Local merchants prosper but foreign relations suffer"}, "no_consequences": {"stat_changes": {"treasury": -20, "happiness": -10}, "story_impact": "Local merchants struggle against foreign competition"}}, {"name": "<PERSON>", "request": "Your Majesty, I wish to open a new bank in the capital. Will you grant me a royal charter?", "yes_consequences": {"stat_changes": {"treasury": 100, "happiness": 5, "population": 10}, "story_impact": "The new bank stimulates economic growth"}, "no_consequences": {"stat_changes": {"treasury": -15, "happiness": -5}, "story_impact": "Economic opportunities are missed without banking services"}}], "farmer": [{"name": "<PERSON>", "request": "Your Majesty, the harvest was poor this year. Will you reduce our taxes so we can survive the winter?", "yes_consequences": {"stat_changes": {"treasury": -30, "happiness": 15, "food": 10}, "story_impact": "The farmers are grateful and work harder next season"}, "no_consequences": {"stat_changes": {"happiness": -15, "food": -10, "population": -5}, "story_impact": "Some farmers abandon their lands, seeking better fortune elsewhere"}}, {"name": "<PERSON>", "request": "Your Majesty, wolves are attacking our livestock. Will you send soldiers to hunt them down?", "yes_consequences": {"stat_changes": {"military": -5, "food": 20, "happiness": 10}, "story_impact": "The wolf threat is eliminated, protecting the farms"}, "no_consequences": {"stat_changes": {"food": -15, "happiness": -10}, "story_impact": "Livestock losses continue to mount as wolves roam freely"}}, {"name": "<PERSON>", "request": "Your Majesty, we need new irrigation channels for our fields. Will you fund this project?", "yes_consequences": {"stat_changes": {"treasury": -40, "food": 25, "population": 5}, "story_impact": "Improved irrigation leads to better harvests"}, "no_consequences": {"stat_changes": {"food": -5, "happiness": -5}, "story_impact": "Crops continue to suffer from poor water management"}}], "soldier": [{"name": "Captain <PERSON>", "request": "Your Majesty, our weapons are old and dull. Will you fund new equipment for the guard?", "yes_consequences": {"stat_changes": {"treasury": -40, "military": 20, "happiness": 5}, "story_impact": "The military is better equipped and morale improves"}, "no_consequences": {"stat_changes": {"military": -10, "happiness": -5}, "story_impact": "The soldiers grumble about inadequate equipment"}}, {"name": "Sir <PERSON>", "request": "Your Majesty, bandits are raiding the eastern roads. Will you authorize a military campaign against them?", "yes_consequences": {"stat_changes": {"military": -10, "treasury": 30, "happiness": 15}, "story_impact": "The roads are secured and trade flows freely again"}, "no_consequences": {"stat_changes": {"treasury": -20, "happiness": -15, "population": -3}, "story_impact": "Bandit raids continue to terrorize merchants and travelers"}}, {"name": "Commander <PERSON><PERSON>", "request": "Your Majesty, we need to recruit more soldiers for the kingdom's defense. Will you approve increased military spending?", "yes_consequences": {"stat_changes": {"treasury": -50, "military": 25, "population": -5}, "story_impact": "Military strength grows but takes people from other work"}, "no_consequences": {"stat_changes": {"military": -5, "happiness": -5}, "story_impact": "The kingdom remains vulnerable with insufficient defenders"}}], "noble": [{"name": "Lord <PERSON>", "request": "Your Majesty, I request funds to host a grand feast for visiting dignitaries. Will you support this diplomatic gesture?", "yes_consequences": {"stat_changes": {"treasury": -60, "happiness": 20, "population": 5}, "story_impact": "The feast impresses visitors and boosts the kingdom's reputation"}, "no_consequences": {"stat_changes": {"happiness": -10}, "story_impact": "The noble is embarrassed and other kingdoms question your hospitality"}}, {"name": "<PERSON>", "request": "Your Majesty, I wish to build a new library in my lands. Will you contribute to this scholarly endeavor?", "yes_consequences": {"stat_changes": {"treasury": -35, "happiness": 25, "population": 3}, "story_impact": "Knowledge flourishes and attracts learned people to the kingdom"}, "no_consequences": {"stat_changes": {"happiness": -8}, "story_impact": "The opportunity to advance learning is lost"}}, {"name": "<PERSON>", "request": "Your Majesty, my castle needs repairs after the recent storms. Will you provide royal funds for restoration?", "yes_consequences": {"stat_changes": {"treasury": -45, "military": 10, "happiness": 10}, "story_impact": "The restored castle strengthens regional defenses"}, "no_consequences": {"stat_changes": {"military": -5, "happiness": -10}, "story_impact": "The crumbling castle weakens the region's defenses"}}], "peasant": [{"name": "Simple Tom", "request": "Your Majesty, my family is starving and I cannot pay my taxes. Will you show mercy and forgive my debt?", "yes_consequences": {"stat_changes": {"treasury": -15, "happiness": 20}, "story_impact": "Your mercy spreads hope among the common folk"}, "no_consequences": {"stat_changes": {"happiness": -20, "population": -2}, "story_impact": "The harsh decision causes fear and resentment among peasants"}}, {"name": "Humble Mary", "request": "Your Majesty, the village well has run dry. Will you send workers to dig a new one?", "yes_consequences": {"stat_changes": {"treasury": -25, "happiness": 25, "population": 5}, "story_impact": "Clean water brings health and growth to the village"}, "no_consequences": {"stat_changes": {"happiness": -15, "population": -5}, "story_impact": "Without water, villagers are forced to abandon their homes"}}, {"name": "Poor Peter", "request": "Your Majesty, my son is sick and I need medicine. Will you allow me to gather herbs from the royal forest?", "yes_consequences": {"stat_changes": {"happiness": 15, "food": -5}, "story_impact": "Your compassion saves a child's life and wins hearts"}, "no_consequences": {"stat_changes": {"happiness": -25, "population": -1}, "story_impact": "The child dies and word spreads of the king's cruelty"}}], "wizard": [{"name": "Arch<PERSON>", "request": "Your Majesty, I require rare crystals for my research into protective wards. Will you fund this magical endeavor?", "yes_consequences": {"stat_changes": {"treasury": -50, "military": 15, "happiness": 10}, "story_impact": "Magical defenses strengthen the kingdom against supernatural threats"}, "no_consequences": {"stat_changes": {"military": -5, "happiness": -5}, "story_impact": "The kingdom remains vulnerable to magical attacks"}}, {"name": "<PERSON>", "request": "Your Majesty, I can create a spell to improve crop yields, but I need access to the royal treasury's gold. Will you trust me?", "yes_consequences": {"stat_changes": {"treasury": -40, "food": 30, "happiness": 15}, "story_impact": "Magical enhancement brings abundant harvests"}, "no_consequences": {"stat_changes": {"food": -10, "happiness": -10}, "story_impact": "Natural crop failures continue without magical aid"}}], "priest": [{"name": "Father <PERSON>", "request": "Your Majesty, the temple roof is leaking and needs repair. Will you contribute to the restoration?", "yes_consequences": {"stat_changes": {"treasury": -30, "happiness": 20, "population": 3}, "story_impact": "The faithful are grateful and the kingdom's piety is strengthened"}, "no_consequences": {"stat_changes": {"happiness": -15}, "story_impact": "Religious devotion wanes as the temple falls into disrepair"}}, {"name": "Sister <PERSON>", "request": "Your Majesty, we wish to establish a hospital for the poor. Will you grant us land and funding?", "yes_consequences": {"stat_changes": {"treasury": -45, "happiness": 30, "population": 8}, "story_impact": "Healthcare for the poor improves life throughout the kingdom"}, "no_consequences": {"stat_changes": {"happiness": -20, "population": -5}, "story_impact": "Disease spreads among the poor without proper medical care"}}], "thief": [{"name": "<PERSON><PERSON>", "request": "Your Majesty, I have information about a plot against you. Will you pardon my past crimes in exchange?", "yes_consequences": {"stat_changes": {"military": 10, "happiness": -10, "treasury": -20}, "story_impact": "The plot is foiled but pardoning criminals sets a dangerous precedent"}, "no_consequences": {"stat_changes": {"military": -15, "happiness": -5}, "story_impact": "Without the warning, the kingdom remains vulnerable to conspiracy"}}, {"name": "Shadow Kate", "request": "Your Majesty, I can steal back the gold that corrupt nobles have hidden. Will you hire me for this task?", "yes_consequences": {"stat_changes": {"treasury": 60, "happiness": -5, "military": -5}, "story_impact": "Stolen wealth is recovered but using thieves damages royal reputation"}, "no_consequences": {"stat_changes": {"treasury": -30, "happiness": -10}, "story_impact": "Corrupt nobles continue to hoard wealth while people suffer"}}], "diplomat": [{"name": "Ambassador <PERSON>", "request": "Your Majesty, a neighboring kingdom offers a trade agreement. Will you accept their terms?", "yes_consequences": {"stat_changes": {"treasury": 40, "happiness": 15, "population": 5}, "story_impact": "Trade flourishes and diplomatic relations strengthen"}, "no_consequences": {"stat_changes": {"treasury": -20, "happiness": -10}, "story_impact": "Isolation hurts the economy and strains foreign relations"}}, {"name": "Envoy <PERSON><PERSON><PERSON>", "request": "Your Majesty, we need to send gifts to secure a peace treaty. Will you authorize this expense?", "yes_consequences": {"stat_changes": {"treasury": -50, "military": 20, "happiness": 25}, "story_impact": "Peace is secured and the kingdom prospers without war"}, "no_consequences": {"stat_changes": {"military": -20, "happiness": -15, "population": -10}, "story_impact": "War breaks out, devastating the kingdom"}}], "inventor": [{"name": "Tin<PERSON>", "request": "Your Majesty, I've designed a new plow that could double crop yields. Will you fund its production?", "yes_consequences": {"stat_changes": {"treasury": -35, "food": 25, "happiness": 15}, "story_impact": "Revolutionary farming technology transforms agriculture"}, "no_consequences": {"stat_changes": {"food": -5, "happiness": -5}, "story_impact": "Traditional farming methods continue to limit food production"}}, {"name": "Craftsman <PERSON>", "request": "Your Majesty, I can build better siege engines for our army. Will you commission this project?", "yes_consequences": {"stat_changes": {"treasury": -45, "military": 30, "happiness": 5}, "story_impact": "Advanced weapons give the kingdom a significant military advantage"}, "no_consequences": {"stat_changes": {"military": -10}, "story_impact": "The army remains equipped with outdated siege technology"}}]}