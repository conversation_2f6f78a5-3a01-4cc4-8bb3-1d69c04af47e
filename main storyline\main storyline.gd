extends Node

@onready var story_text_label: Label = InGameUi.get_node("Label")

# This dictionary will hold the entire decision tree after loading.
var story_data: Dictionary = {}

# This string holds the key for the current node we are on (e.g., "start").
var current_node_id: String = ""


# _ready() is called when the node and its children have entered the scene tree.
func _ready() -> void:
	# Load the story data and start the game.
	start_game()


# Loads the decision tree data from the JSON FileAccess.
func load_story_data() -> bool:
	# Open the FileAccess for reading.
	var file = FileAccess.open("res://decisions.json", FileAccess.READ)
	if file:
	# Read the entire FileAccess content as text.
		var json_text = file.get_as_text()
		file.close() # Always close the FileAccess when you're done with it.
		
		# Parse the JSON text into a Godot Dictionary.
		var json = JSON.new()
		var parse_result = json.parse(json_text)
		if parse_result == OK:
			# If parsing was successful, store the data.
			story_data = json.data
			return true
	return false


# Initializes the game.
func start_game() -> void:
	if not load_story_data():
		story_text_label.text = "Error: Could not load story data. Check console for details."
		return
	
	# Set the starting point of the story.
	current_node_id = "start"


# This function is called when either the "Yes" or "No" button is pressed.
func _on_choice_made(choice: String) -> void:
	# Get the data for the current node.
	var node_data: Dictionary = story_data[current_node_id]
	
	# Find the ID of the next node based on the player's choice.
	var next_node_id = node_data.get(choice)
	
	if next_node_id:
		# If a next node exists, update our state and refresh the display.
		current_node_id = next_node_id
	else:
		# This should not happen if the JSON is structured correctly.
		push_error("Choice '" + choice + "' not found for node '" + current_node_id + "'")
