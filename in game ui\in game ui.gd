extends Control

func _ready():
	add_story_line("=== THE KINGDOM OF VALDRIS ===")
	add_story_line("Your father, the late King <PERSON><PERSON><PERSON>, has passed away unexpectedly.")
	add_story_line("You have inherited a kingdom in turmoil. Dark clouds gather on the horizon...")
	add_story_line("Your decisions will shape not only the kingdom's fate, but the very story of your reign.")
	add_story_line("")


func add_story_line(story: String):
	var label = Label.new()
	label.text = story
	$ScrollContainer/VBoxContainer.add_child(label)

func _on_button_pressed():
	print("YES button pressed")
	GameManager.make_decision("YES")

func _on_button_2_pressed():
	print("NO button pressed")
	GameManager.make_decision("NO")
